# OCR Document Scanning Implementation

## Overview
The LC Work Flow app now includes fully functional OCR (Optical Character Recognition) capabilities for scanning and extracting text from ID documents. This feature automatically populates customer information forms from captured document images.

## Features Implemented

### 1. Document Scanning
- **Camera Integration**: Uses device camera to capture ID documents
- **Image Quality**: Optimized at 90% quality for good OCR results
- **Real-time Processing**: Immediate text recognition after image capture

### 2. Text Recognition
- **Multi-script Support**: Handles both Latin and Khmer text
- **Dual OCR Engine**: Uses Latin script recognition with Chinese script fallback for better Khmer character detection
- **Text Cleanup**: Removes duplicates and normalizes spacing

### 3. Information Extraction
- **Phone Numbers**: Extracts Cambodian phone number formats (+855, 855, 0xx)
- **ID Numbers**: Identifies 9-12 digit ID numbers
- **Names**: Separates Khmer and Latin names
- **Dates**: Recognizes various date formats (DD/MM/YYYY, DD-MM-YYYY)

### 4. Auto-Population
- **Smart Field Filling**: Only fills empty fields to preserve user input
- **Date Parsing**: Converts recognized dates to proper DateTime objects
- **Validation**: Ensures extracted data meets format requirements

## How to Use

### For Users
1. Navigate to the "Add Customer" screen
2. Select a document type from the dropdown
3. Tap the "Scan Document" button
4. Point camera at the ID document and capture
5. Review the extracted information in the popup dialog
6. Verify and correct any auto-filled fields as needed

### For Developers

#### Key Files
- `lib/services/khmer_text_recognizer.dart` - Core OCR service
- `lib/screen/dashboard/screens/add_customer_screen.dart` - Integration implementation
- `lib/screen/dashboard/screens/add_customer/borrower_info_step.dart` - UI components

#### Usage Example
```dart
// Capture and process document
final recognizedText = await KhmerTextRecognizer.recognizeText(imageFile);
final extractedInfo = KhmerTextRecognizer.extractInformation(recognizedText);

// Use extracted information
if (extractedInfo.containsKey('phone')) {
  phoneController.text = extractedInfo['phone']!;
}
```

## Technical Details

### OCR Engine Configuration
- **Primary**: TextRecognitionScript.latin for mixed content
- **Secondary**: TextRecognitionScript.chinese for Khmer characters
- **Fallback**: Graceful degradation if secondary recognition fails

### Text Processing Pipeline
1. **Image Capture** → Camera with optimized settings
2. **OCR Processing** → Dual-script recognition
3. **Text Cleanup** → Remove duplicates and normalize
4. **Information Extraction** → Pattern matching for specific data types
5. **Field Population** → Smart auto-fill with validation

### Supported Document Types
- Khmer National ID (អត្តសញ្ញាណប័ណ្ណសញ្ជាតិខ្មែរ)
- Passport (លិខិតឆ្លងដែន)
- Driver's License (ប័ណ្ណបើកបរ)
- Government Officer Card (ប័ណ្ណមន្ត្រីរាជការ)
- Monk Card (ប័ណ្ណព្រះសង្ឃ)
- Family Book (សៀវភៅគ្រួសារ)
- Birth Certificate (សំបុត្រកំណើត)

## Error Handling
- **Camera Access**: Proper permission handling
- **OCR Failures**: Graceful fallback with user notification
- **Invalid Text**: Filters out non-meaningful extractions
- **Network Issues**: Offline OCR processing (no internet required)

## Performance Considerations
- **Image Quality**: 90% compression for optimal OCR vs. file size
- **Processing Time**: Typically 2-5 seconds for document processing
- **Memory Usage**: Efficient cleanup of OCR resources
- **Battery Impact**: Minimal due to local processing

## Future Enhancements
- Support for additional document types
- Improved Khmer text recognition accuracy
- Batch document processing
- OCR confidence scoring
- Custom training for specific document layouts

## Testing
Run the OCR tests with:
```bash
flutter test test/services/khmer_text_recognizer_test.dart
```

## Dependencies
- `google_mlkit_text_recognition: ^0.15.0` - Core OCR functionality
- `image_picker: ^1.0.7` - Camera integration
- `get: ^4.7.2` - State management and navigation

## Permissions Required
- `android.permission.CAMERA` - Camera access for document capture
- `android.permission.READ_EXTERNAL_STORAGE` - Image file access
- `android.permission.WRITE_EXTERNAL_STORAGE` - Temporary file storage
