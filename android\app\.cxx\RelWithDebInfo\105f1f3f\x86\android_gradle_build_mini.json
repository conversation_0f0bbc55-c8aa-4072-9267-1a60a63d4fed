{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\lc_work_flow\\android\\app\\.cxx\\RelWithDebInfo\\105f1f3f\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Downloads\\lc_work_flow\\android\\app\\.cxx\\RelWithDebInfo\\105f1f3f\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}