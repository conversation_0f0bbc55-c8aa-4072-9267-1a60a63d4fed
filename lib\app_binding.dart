import 'package:get/get.dart';
import 'package:lc_work_flow/screen/dashboard/controllers/dashboard_controller.dart';
import 'package:lc_work_flow/screen/login/controllers/login_controller.dart';
import 'package:lc_work_flow/screen/splash/controllers/splash_controller.dart';

class AppBinding implements Bindings {
  @override
  void dependencies() {
    // Register global dependencies here when needed
    Get.put(LoginController());
    Get.put(SplashController());
    Get.put(DashboardController());
  }
}
