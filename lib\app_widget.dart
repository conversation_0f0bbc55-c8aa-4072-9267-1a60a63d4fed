import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app_binding.dart';
import 'core/routes/app_pages.dart';

// Main app widget and routing entry point
class AppWidget extends StatelessWidget {
  const AppWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      initialBinding: AppBinding(),
      initialRoute: AppPages.initial,
      getPages: AppPages.routes,
      theme: ThemeData(
        textTheme: const TextTheme(
          bodyMedium: TextStyle(fontSize: 12.0),
          bodySmall: TextStyle(fontSize: 11.0),
          labelLarge: TextStyle(fontSize: 12.0),
        ),
        inputDecorationTheme: const InputDecorationTheme(
          contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          labelStyle: TextStyle(fontSize: 12.0),
          hintStyle: TextStyle(fontSize: 12.0),
        ),
      ),
    );
  }
}
