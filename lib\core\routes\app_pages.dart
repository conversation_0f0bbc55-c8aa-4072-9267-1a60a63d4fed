import 'package:get/get.dart';
import 'package:lc_work_flow/screen/splash/screens/splash_screen.dart';

import '../../screen/dashboard/bindings/dashboard_binding.dart';
import '../../screen/dashboard/screens/dashboard_screen.dart';
import '../../screen/login/bindings/login_binding.dart';
import '../../screen/login/screens/login_screen.dart';
import '../../screen/splash/bindings/splash_binding.dart';
part 'app_routes.dart';

// Defines app routes and navigation structure
class AppPages {
  static const initial = Routes.splash;

  static final routes = [
    GetPage(
      name: Routes.splash,
      page: () => const SplashScreen(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: Routes.login,
      page: () => LoginScreen(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: Routes.dashboard,
      page: () => const DashboardScreen(),
      binding: DashboardBinding(),
    ),
  ];
}
