import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/product_type.dart' as product_type;

enum LoanStatus { pending, approved, disbursed, completed, rejected }

enum IdCardType { nid, passport, driverLicense, cambodianIdentity, none }

enum LoanPurposeType { agriculture, commerce, services, transportation, construction, family, other }


class Customer {
  // Section 1: Borrower Information
  // Section 1: Borrower Information
  final IdCardType? idCardType;
  final String? idCardPhotoPath;
  final String? fullNameKhmer;
  final String? fullNameLatin;
  final DateTime? dateOfBirth;
  final String? idNumber;
  final String? portfolioOfficerName;

  // Section 2: Loan/Pawn Details
  final double? requestedAmount;
  final List<LoanPurposeType>? loanPurposes;
  final String? purposeDetails;
  final product_type.ProductType? productType;
  final String? desiredLoanTerm;
  final DateTime? requestedDisbursementDate;

  // Section 3: Borrower Photos
  final String? borrowerNidPhotoPath;
  final String? borrowerHomeOrLandPhotoPath;
  final String? borrowerBusinessPhotoPath;

  // Section 4: Guarantor Photos
  final String? guarantorName;
  final String? guarantorPhone;
  final String? guarantorNidPhotoPath;
  final String? guarantorHomeOrLandPhotoPath;
  final String? guarantorBusinessPhotoPath;
  final String? profilePhotoPath;

  final String id;
  final String name;
  final String phone;
  final String nid;
  final String? profileImage;
  final double loanAmount;
  final DateTime loanStartDate;
  final DateTime? loanEndDate;
  final LoanStatus loanStatus;
  final double? interestRate;
  final String? loanPurpose;

  Customer({
    // Borrower Info
    this.idCardType,
    this.idCardPhotoPath,
    this.fullNameKhmer,
    this.fullNameLatin,
    this.dateOfBirth,
    this.idNumber,
    this.portfolioOfficerName,

    // Loan Details
    this.requestedAmount,
    this.loanPurposes,
    this.purposeDetails,
    this.productType,
    this.desiredLoanTerm,
    this.requestedDisbursementDate,

    // Borrower Photos
    this.borrowerNidPhotoPath,
    this.borrowerHomeOrLandPhotoPath,
    this.borrowerBusinessPhotoPath,

    // Guarantor Photos
    this.guarantorName,
    this.guarantorPhone,
    this.guarantorNidPhotoPath,
    this.guarantorHomeOrLandPhotoPath,
    this.guarantorBusinessPhotoPath,
    this.profilePhotoPath,

    // Original fields
    required this.id,
    required this.name, // Keep for now to avoid breaking UI, will replace with fullNameLatin
    required this.phone,
    required this.nid,
    this.profileImage,
    required this.loanAmount,
    required this.loanStartDate,
    this.loanEndDate,
    required this.loanStatus,
    this.interestRate,
    this.loanPurpose,
  });

  String get formattedStartDate {
    return '${_getMonthName(loanStartDate.month)} ${loanStartDate.day}, ${loanStartDate.year}';
  }

  // Use fullNameLatin if available, otherwise fallback to the original name field.
  String get displayName => fullNameLatin ?? name;

  Color get statusColor {
    switch (loanStatus) {
      case LoanStatus.pending:
        return Colors.orange;
      case LoanStatus.approved:
        return Colors.blue;
      case LoanStatus.disbursed:
        return Colors.green;
      case LoanStatus.completed:
        return Colors.purple;
      case LoanStatus.rejected:
        return Colors.red;
    }
  }

  String _getMonthName(int month) {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return monthNames[month - 1];
  }

  IconData get statusIcon {
    switch (loanStatus) {
      case LoanStatus.pending:
        return Icons.pending_actions;
      case LoanStatus.approved:
        return Icons.verified;
      case LoanStatus.disbursed:
        return Icons.monetization_on;
      case LoanStatus.completed:
        return Icons.check_circle;
      case LoanStatus.rejected:
        return Icons.cancel;
    }
  }
}
