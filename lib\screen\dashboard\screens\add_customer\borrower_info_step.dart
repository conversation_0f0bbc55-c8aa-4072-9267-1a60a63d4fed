import 'package:flutter/material.dart';

import 'package:lc_work_flow/widgets/customer/customer_section_title.dart';
import 'package:lc_work_flow/widgets/customer/customer_text_field.dart';
import 'package:lc_work_flow/widgets/customer/customer_date_picker_field.dart';
import 'package:lc_work_flow/widgets/customer/customer_document_type_dropdown.dart';
import 'package:lc_work_flow/models/document_type.dart';

class BorrowerInfoStep extends StatelessWidget {
  final TextEditingController fullNameKhmerController;
  final TextEditingController fullNameLatinController;
  final TextEditingController phoneController;
  final TextEditingController idNumberController;
  final DocumentType? selectedDocumentType;
  final DateTime? dateOfBirth;
  final Function(DocumentType?) onDocumentTypeChanged;
  final Function(DateTime?) onDateOfBirthChanged;
  final VoidCallback? onScanDocument;

  const BorrowerInfoStep({
    super.key,
    required this.fullNameKhmerController,
    required this.fullNameLatinController,
    required this.phoneController,
    required this.idNumberController,
    required this.selectedDocumentType,
    required this.dateOfBirth,
    required this.onDocumentTypeChanged,
    required this.onDateOfBirthChanged,
    this.onScanDocument,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Document Selection Section
          _buildDocumentSelectionSection(context),
          const SizedBox(height: 32),

          // Borrower Information Section
          CustomerSectionTitle(
            title: 'Borrower Information',
            icon: Icons.person_outline,
          ),
          const SizedBox(height: 20),
          CustomerTextField(
            enabled: selectedDocumentType != null,
            controller: phoneController,
            label: 'Phone Number',
            keyboardType: TextInputType.phone,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a phone number';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          CustomerTextField(
            enabled: selectedDocumentType != null,
            label: 'Full Name (Khmer)',
            controller: fullNameKhmerController,
            hint: 'Enter full name in Khmer',
            validator:
                (v) =>
                    v == null || v.isEmpty
                        ? 'Please enter the full name in Khmer'
                        : null,
          ),
          const SizedBox(height: 16),
          CustomerTextField(
            enabled: selectedDocumentType != null,
            label: 'Full Name (Latin)',
            controller: fullNameLatinController,
            hint: 'Enter full name in Latin',
            validator:
                (v) =>
                    v == null || v.isEmpty
                        ? 'Please enter the full name in Latin'
                        : null,
          ),
          const SizedBox(height: 16),
          CustomerTextField(
            enabled: selectedDocumentType != null,
            label: 'ID Number',
            controller: idNumberController,
            hint: 'Enter ID number',
            validator:
                (v) =>
                    v == null || v.isEmpty
                        ? 'Please enter the ID number'
                        : null,
          ),
          const SizedBox(height: 16),
          CustomerDatePickerField(
            enabled: selectedDocumentType != null,
            label: 'Date of Birth',
            selectedDate: dateOfBirth,
            onDateSelected: onDateOfBirthChanged,
            validator: (date) {
              if (date == null) {
                return 'Please select a date of birth';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// Enhanced document selection section with modern design
  Widget _buildDocumentSelectionSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF12306E).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.document_scanner,
                  color: Color(0xFF12306E),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Document Selection',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF12306E),
                      ),
                    ),
                    Text(
                      'Start by selecting a document type',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          CustomerDocumentTypeDropdown(
            selectedDocumentType: selectedDocumentType,
            onChanged: onDocumentTypeChanged,
          ),
          if (selectedDocumentType != null) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: Container(
                height: 48,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF12306E), Color(0xFF1E4A8C)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF12306E).withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: onScanDocument,
                    child: const Center(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.document_scanner,
                            color: Colors.white,
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Scan Document',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
