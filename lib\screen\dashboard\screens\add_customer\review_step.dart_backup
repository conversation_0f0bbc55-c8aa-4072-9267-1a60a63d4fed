import 'dart:io';
import 'package:flutter/material.dart';

class ReviewStep extends StatelessWidget {
  // Placeholder data, replace with your actual models
  final Map<String, dynamic> borrowerInfo;
  final Map<String, dynamic> loanInfo;
  final List<Map<String, dynamic>> collateralList;
  final Map<String, dynamic>? guarantor;
  final List<Map<String, dynamic>> documents;
  final Map<String, dynamic> photos;
  final String? officerName;
  final VoidCallback? onEditBorrower;
  final VoidCallback? onEditLoan;
  final VoidCallback? onEditCollateral;
  final VoidCallback? onEditGuarantor;
  final VoidCallback? onEditDocuments;
  final VoidCallback? onEditPhotos;
  final VoidCallback? onEditOfficer;
  final VoidCallback? onBack;
  final VoidCallback? onConfirm;

  const ReviewStep({
    super.key,
    required this.borrowerInfo,
    required this.loanInfo,
    required this.collateralList,
    this.guarantor,
    required this.documents,
    required this.photos,
    this.officerName,
    this.onEditBorrower,
    this.onEditLoan,
    this.onEditCollateral,
    this.onEditGuarantor,
    this.onEditDocuments,
    this.onEditPhotos,
    this.onEditOfficer,
    this.onBack,
    this.onConfirm,
  });

  Widget _buildSection({
    required BuildContext context,
    required String title,
    required List<Widget> children,
    VoidCallback? onEdit,
    bool hasError = false,
  }) {
    return ConstrainedBox(
      constraints: const BoxConstraints(minHeight: 100),
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                    child: Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ),
                  ),
                  if (onEdit != null)
                    IconButton(
                      icon: const Icon(Icons.edit, size: 20),
                      onPressed: onEdit,
                      tooltip: 'Edit',
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                      visualDensity: VisualDensity.compact,
                    ),
                  if (hasError)
                    const Padding(
                      padding: EdgeInsets.only(left: 4.0),
                      child: Icon(Icons.error, color: Colors.red, size: 20),
                    ),
                ],
              ),
              const SizedBox(height: 8),
              ...children,
            ],
          ),
        ),
      ),
    );
  }

  Widget buildImageThumbnail(String? path) {
    const double thumbnailSize = 60.0;

    Widget errorWidget(IconData icon, Color color) {
      return ConstrainedBox(
        constraints: const BoxConstraints.tightFor(
          width: thumbnailSize,
          height: thumbnailSize,
        ),
        child: Container(
          color: Colors.grey[200],
          child: Icon(icon, color: color),
        ),
      );
    }

    if (path == null || path.isEmpty) {
      return errorWidget(Icons.image_not_supported, Colors.grey);
    }

    // Handle both network and asset images
    final ImageProvider image;
    try {
      image =
          path.startsWith('http') || path.startsWith('https')
              ? NetworkImage(path)
              : FileImage(File(path)) as ImageProvider;
    } catch (e) {
      return errorWidget(Icons.error_outline, Colors.red);
    }

    return ConstrainedBox(
      constraints: const BoxConstraints.tightFor(
        width: thumbnailSize,
        height: thumbnailSize,
      ),
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.grey[200],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image(
            image: image,
            fit: BoxFit.cover,
            errorBuilder:
                (context, error, stackTrace) =>
                    const Icon(Icons.broken_image, color: Colors.grey),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final _ = MediaQuery.of(context).padding.bottom;

    // bool borrowerError = false;r
    // bool loanError = false;
    // bool collateralError = false;
    // bool documentsError = false;
    // bool photosError = false;
    // bool officerError = false;
    // bool guarantorError = false;

    return Scaffold(
      appBar: AppBar(title: const Text('Review Customer'), centerTitle: true),
      body: LayoutBuilder(
        builder:
            (context, constraints) => Column(
              children: [
                Expanded(
                  child: ListView(
                    padding: EdgeInsets.zero,
                    children: [
                      // _buildSection(
                      //   context: context,
                      //   title: 'Borrower Information',
                      //   onEdit: onEditBorrower,
                      //   hasError: borrowerError,
                      //   children: [
                      //     ListTile(
                      //       title: const Text('Document Type'),
                      //       subtitle: Text(borrowerInfo['documentType'] ?? '-'),
                      //     ),
                      //     ListTile(
                      //       title: const Text('ID Number'),
                      //       subtitle: Text(borrowerInfo['idNumber'] ?? '-'),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Full Name (Khmer)'),
                      //       subtitle: Text(
                      //         borrowerInfo['fullNameKhmer'] ?? '-',
                      //       ),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Full Name (Latin)'),
                      //       subtitle: Text(
                      //         borrowerInfo['fullNameLatin'] ?? '-',
                      //       ),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Phone Number'),
                      //       subtitle: Text(borrowerInfo['phone'] ?? '-'),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Date of Birth'),
                      //       subtitle: Text(borrowerInfo['dateOfBirth'] ?? '-'),
                      //     ),
                      //   ],
                      // ),
                      // _buildSection(
                      //   context: context,
                      //   title: 'Loan Information',
                      //   onEdit: onEditLoan,
                      //   hasError: loanError,
                      //   children: [
                      //     ListTile(
                      //       title: const Text('Requested Amount'),
                      //       subtitle: Text(
                      //         loanInfo['requestedAmount']?.toString() ?? '-',
                      //       ),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Product Type'),
                      //       subtitle: Text(loanInfo['productType'] ?? '-'),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Interest Rate'),
                      //       subtitle: Text(
                      //         '${loanInfo['interestRate']?.toString() ?? '-'}%',
                      //       ),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Loan Term'),
                      //       subtitle: Text(loanInfo['loanTerm'] ?? '-'),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Loan Purpose(s)'),
                      //       subtitle: Text(
                      //         (loanInfo['loanPurposes'] as List?)?.join(', ') ??
                      //             '-',
                      //       ),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Purpose Details'),
                      //       subtitle: Text(loanInfo['purposeDetails'] ?? '-'),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Disbursement Date'),
                      //       subtitle: Text(loanInfo['disbursementDate'] ?? '-'),
                      //     ),
                      //   ],
                      // ),
                      // _buildSection(
                      //   context: context,
                      //   title: 'Collateral',
                      //   onEdit: onEditCollateral,
                      //   hasError: collateralError,
                      //   children:
                      //       collateralList.isEmpty
                      //           ? [const Text('No collateral selected.')]
                      //           : collateralList
                      //               .map(
                      //                 (c) => ListTile(
                      //                   title: Text(c['type'] ?? '-'),
                      //                   subtitle: Text(c['description'] ?? ''),
                      //                   trailing: _buildImageThumbnail(
                      //                     c['image'],
                      //                   ),
                      //                 ),
                      //               )
                      //               .toList(),
                      // ),
                      // _buildSection(
                      //   context: context,
                      //   title: 'Guarantor',
                      //   onEdit: onEditGuarantor,
                      //   hasError: guarantorError,
                      //   children: [
                      //     ListTile(
                      //       title: const Text('Name'),
                      //       subtitle: Text(
                      //         guarantor?['name']?.toString() ?? '-',
                      //       ),
                      //     ),
                      //     ListTile(
                      //       title: const Text('Phone'),
                      //       subtitle: Text(
                      //         guarantor?['phone']?.toString() ?? '-',
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      // _buildSection(
                      //   context: context,
                      //   title: 'Documents',
                      //   onEdit: onEditDocuments,
                      //   hasError: documentsError,
                      //   children:
                      //       documents.isEmpty
                      //           ? [const Text('No documents uploaded.')]
                      //           : documents
                      //               .map(
                      //                 (doc) => ListTile(
                      //                   title: Text(doc['type'] ?? 'Document'),
                      //                   trailing: _buildImageThumbnail(
                      //                     doc['image'],
                      //                   ),
                      //                 ),
                      //               )
                      //               .toList(),
                      // ),
                      // _buildSection(
                      //   context: context,
                      //   title: 'Photos',
                      //   onEdit: onEditPhotos,
                      //   hasError: photosError,
                      //   children: [
                      //     Wrap(
                      //       spacing: 8,
                      //       runSpacing: 8,
                      //       children: [
                      //         _buildPhotoItem(
                      //           'Borrower NID',
                      //           photos['borrowerNidPhoto'],
                      //         ),
                      //         _buildPhotoItem(
                      //           'Borrower Home/Land',
                      //           photos['borrowerHomePhoto'],
                      //         ),
                      //         _buildPhotoItem(
                      //           'Borrower Business',
                      //           photos['borrowerBusinessPhoto'],
                      //         ),
                      //         _buildPhotoItem(
                      //           'Guarantor NID',
                      //           photos['guarantorNidPhoto'],
                      //         ),
                      //         _buildPhotoItem(
                      //           'Guarantor Home/Land',
                      //           photos['guarantorHomePhoto'],
                      //         ),
                      //         _buildPhotoItem(
                      //           'Guarantor Business',
                      //           photos['guarantorBusinessPhoto'],
                      //         ),
                      //         _buildPhotoItem(
                      //           'Profile Photo',
                      //           photos['profilePhoto'],
                      //         ),
                      //       ],
                      //     ),
                      //   ],
                      // ),
                      // _buildSection(
                      //   context: context,
                      //   title: 'Officer Info',
                      //   onEdit: onEditOfficer,
                      //   hasError: officerError,
                      //   children: [
                      //     ListTile(
                      //       title: const Text('Portfolio Officer Name'),
                      //       subtitle: Text(officerName ?? '-'),
                      //     ),
                      //   ],
                      // ),
                    ],
                  ),
                ),
                // SizedBox(
                //   height: 80,
                //   child: Container(
                //     width: double.infinity,
                //     decoration: BoxDecoration(
                //       color: Theme.of(context).scaffoldBackgroundColor,
                //       boxShadow: [
                //         BoxShadow(
                //           color: Colors.black.withOpacity(0.1),
                //           blurRadius: 4,
                //           offset: const Offset(0, -2),
                //         ),
                //       ],
                //     ),
                //     padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                //     child: Row(
                //       children: [
                //         Expanded(
                //           child: OutlinedButton(
                //             onPressed: onBack,
                //             child: const Text('Back'),
                //           ),
                //         ),
                //         const SizedBox(width: 16),
                //         Expanded(
                //           child: ElevatedButton(
                //             onPressed: onConfirm,
                //             style: ElevatedButton.styleFrom(
                //               padding: const EdgeInsets.symmetric(vertical: 16),
                //               textStyle: const TextStyle(fontSize: 16),
                //               shape: RoundedRectangleBorder(
                //                 borderRadius: BorderRadius.circular(12),
                //               ),
                //             ),
                //             child: const Text('Confirm & Submit'),
                //           ),
                //         ),
                //       ],
                //     ),
                //   ),
                // ),
              ],
            ),
      ),
    );
  }

  // Widget _buildPhotoItem(String label, String? photoPath) {
  //   return Column(
  //     children: [
  //       _buildImageThumbnail(photoPath),
  //       Text(label, textAlign: TextAlign.center),
  //     ],
  //   );
  // }
}
