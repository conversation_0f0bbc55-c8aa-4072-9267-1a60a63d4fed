// import 'dart:io';
import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/collateral_type.dart';
// import 'package:lc_work_flow/widgets/customer/customer_photo_capture.dart';

class CustomerCollateralSection extends StatelessWidget {
  final Set<CollateralType> selectedCollateralTypes;
  final Function(CollateralType, bool) onCollateralTypeChanged;

  const CustomerCollateralSection({
    super.key,
    required this.selectedCollateralTypes,
    required this.onCollateralTypeChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'ប្រភេទធានា/Collateral Type',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        ...CollateralType.values.map(
          (type) => CheckboxListTile(
            value: selectedCollateralTypes.contains(type),
            title: Text(type.displayName),
            onChanged: (selected) {
              onCollateralTypeChanged(type, selected ?? false);
            },
          ),
        ),
        const SizedBox(height: 16),
        // Remove image capture widgets from step 2
      ],
    );
  }
}
