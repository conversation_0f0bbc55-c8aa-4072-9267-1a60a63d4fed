import 'dart:io';
import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/collateral_type.dart';
import 'package:lc_work_flow/widgets/customer/customer_photo_capture.dart';

class CustomerDocumentsSection extends StatelessWidget {
  final File? idCardImage;
  final Map<CollateralType, List<File>> collateralImages;
  final Set<CollateralType> selectedCollateralTypes;
  final Function(CollateralType, List<File>) onImagePicked;

  const CustomerDocumentsSection({
    super.key,
    required this.idCardImage,
    required this.collateralImages,
    required this.selectedCollateralTypes,
    required this.onImagePicked,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomerPhotoCapture(
          title: 'ID Card',
          imageFiles: idCardImage != null ? [idCardImage!] : [],
          onImagesChanged:
              (photos) => onImagePicked(CollateralType.hardTitle, photos),
          isSelfie: false,
        ),
        ...selectedCollateralTypes.map((type) {
          List<File> imageFiles = collateralImages[type] ?? [];
          return CustomerPhotoCapture(
            title: type.displayName,
            imageFiles: imageFiles,
            onImagesChanged: (photos) => onImagePicked(type, photos),
            isSelfie: false,
          );
        }),
      ],
    );
  }
}
