import 'package:flutter/material.dart';

class CustomerGuarantorSection extends StatelessWidget {
  final TextEditingController guarantorNameController;
  final TextEditingController guarantorPhoneController;
  final FocusNode guarantorNameFocusNode;
  final FocusNode guarantorPhoneFocusNode;
  final bool show;

  const CustomerGuarantorSection({
    super.key,
    required this.guarantorNameController,
    required this.guarantorPhoneController,
    required this.guarantorNameFocusNode,
    required this.guarantorPhoneFocusNode,
    required this.show,
  });

  @override
  Widget build(BuildContext context) {
    if (!show) return SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ព័ត៌មានអ្នកធានា/អ្នករួមខ្ចី',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          TextField(
            controller: guarantorNameController,
            focusNode: guarantorNameFocusNode,
            textInputAction: TextInputAction.next,
            decoration: const InputDecoration(
              labelText: 'Guarantor/Co-borrower Name',
              hintText: 'Enter name',
              border: OutlineInputBorder(),
            ),
            onSubmitted: (_) {
              FocusScope.of(context).requestFocus(guarantorPhoneFocusNode);
            },
          ),
          const SizedBox(height: 8),
          TextField(
            controller: guarantorPhoneController,
            focusNode: guarantorPhoneFocusNode,
            textInputAction: TextInputAction.done,
            decoration: const InputDecoration(
              labelText: 'Guarantor/Co-borrower Phone',
              hintText: 'Enter phone number',
              border: OutlineInputBorder(),
            ),
            onSubmitted: (_) {
              FocusScope.of(context).unfocus();
            },
          ),
        ],
      ),
    );
  }
}
