import 'package:flutter/material.dart';
import 'package:lc_work_flow/models/product_type.dart' as ProductTypeDropdown;

class CustomerLoanInfoSection extends StatelessWidget {
  final TextEditingController requestedAmountController;
  final ProductTypeDropdown.ProductType? selectedProductType;
  final Function(ProductTypeDropdown.ProductType?) onProductTypeChanged;
  final TextEditingController loanTermController;

  const CustomerLoanInfoSection({
    super.key,
    required this.requestedAmountController,
    required this.selectedProductType,
    required this.onProductTypeChanged,
    required this.loanTermController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: requestedAmountController,
          decoration: const InputDecoration(
            labelText: 'Requested Amount',
            hintText: 'Enter requested amount',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<ProductTypeDropdown.ProductType>(
          value: selectedProductType,
          onChanged: onProductTypeChanged,
          decoration: const InputDecoration(
            labelText: 'Product Type',
            border: OutlineInputBorder(),
            filled: true,
          ),
          items:
              ProductTypeDropdown.ProductType.values.map((type) {
                return DropdownMenuItem<ProductTypeDropdown.ProductType>(
                  value: type,
                  child: Text(type.displayName),
                );
              }).toList(),
        ),
        const SizedBox(height: 16),
        TextField(
          controller: loanTermController,
          decoration: const InputDecoration(
            labelText: 'Loan Term',
            hintText: 'Enter loan term (months)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
        ),
      ],
    );
  }
}
