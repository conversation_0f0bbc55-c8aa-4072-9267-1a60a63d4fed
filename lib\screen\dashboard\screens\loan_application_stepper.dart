import 'package:flutter/material.dart';

class LoanApplicationStepper extends StatefulWidget {
  const LoanApplicationStepper({super.key});

  @override
  State<LoanApplicationStepper> createState() => _LoanApplicationStepperState();
}

class _LoanApplicationStepperState extends State<LoanApplicationStepper> {
  int _currentStep = 0;

  @override
  Widget build(BuildContext context) {
    return Stepper(
      type: StepperType.vertical,
      currentStep: _currentStep,
      onStepContinue: () {
        if (_currentStep < 4) {
          setState(() => _currentStep += 1);
        }
      },
      onStepCancel: () {
        if (_currentStep > 0) {
          setState(() => _currentStep -= 1);
        }
      },
      steps: [
        Step(
          title: const Text('Customer Information'),
          isActive: _currentStep >= 0,
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              TextField(decoration: InputDecoration(labelText: 'Khmer Name')),
              TextField(decoration: InputDecoration(labelText: 'Latin Name')),
              TextField(
                decoration: InputDecoration(labelText: 'Client Status'),
              ),
              TextField(decoration: InputDecoration(labelText: 'Contact Info')),
            ],
          ),
        ),
        Step(
          title: const Text('Loan/Pawn Details'),
          isActive: _currentStep >= 1,
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              TextField(
                decoration: InputDecoration(labelText: 'Requested Amount'),
              ),
              TextField(decoration: InputDecoration(labelText: 'Purpose')),
              TextField(decoration: InputDecoration(labelText: 'Product Type')),
              TextField(decoration: InputDecoration(labelText: 'Loan Term')),
              TextField(
                decoration: InputDecoration(labelText: 'Disbursement Date'),
              ),
            ],
          ),
        ),
        Step(
          title: const Text('Financial Status'),
          isActive: _currentStep >= 2,
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              TextField(
                decoration: InputDecoration(labelText: 'Existing Debts'),
              ),
              TextField(decoration: InputDecoration(labelText: 'Income')),
              TextField(decoration: InputDecoration(labelText: 'Expenses')),
            ],
          ),
        ),
        Step(
          title: const Text('Collateral Information'),
          isActive: _currentStep >= 3,
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              TextField(
                decoration: InputDecoration(labelText: 'Pledged Assets'),
              ),
              TextField(
                decoration: InputDecoration(
                  labelText: 'Existing Assets Checklist',
                ),
              ),
            ],
          ),
        ),
        Step(
          title: const Text('Declaration & Consent'),
          isActive: _currentStep >= 4,
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              Text('Digital Signature/Confirmation Placeholder'),
            ],
          ),
        ),
      ],
    );
  }
}
