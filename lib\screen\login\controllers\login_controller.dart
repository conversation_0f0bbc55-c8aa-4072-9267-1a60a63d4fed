import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lc_work_flow/core/routes/app_pages.dart';

class LoginController extends GetxController {
  final usernameController = TextEditingController();
  final passwordController = TextEditingController();
  final isObscure = true.obs; // observable for password visibility

  @override
  void onClose() {
    usernameController.dispose();
    passwordController.dispose();
    super.onClose();
  }

  void login() {
    String username = usernameController.text;
    String password = passwordController.text;

    // Basic Validation
    if (username.isEmpty || password.isEmpty) {
      Get.snackbar(
        'Error',
        'Please fill in all fields',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    // In a real app, you'd call an API here to authenticate
    // Example simulated login:
    if (username == 'admin' && password == 'password') {
      Get.offNamed(Routes.dashboard);
    } else {
      Get.snackbar(
        'Error',
        'Invalid username or password',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
