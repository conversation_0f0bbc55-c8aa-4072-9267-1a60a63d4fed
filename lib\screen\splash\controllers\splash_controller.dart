import 'package:get/get.dart';
import '../../../../core/routes/app_pages.dart';

class SplashController extends GetxController {
  @override
  void onReady() {
    super.onReady();
    // Simulate some loading/initialization work
    // After 3 seconds, navigate to the login screen
    Future.delayed(const Duration(seconds: 3), () {
      Get.offNamed(Routes.login); // Navigate to login after splash
    });
  }
}
