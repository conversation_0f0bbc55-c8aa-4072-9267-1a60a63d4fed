import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/splash_controller.dart';

class SplashScreen extends GetView<SplashController> {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Animated logo
            TweenAnimationBuilder<double>(
              tween: Tween<double>(begin: 0.0, end: 1.0),
              duration: const Duration(seconds: 2),
              curve: Curves.easeOutBack,
              builder: (context, value, child) {
                return Opacity(
                  opacity: value.clamp(0.0, 1.0),
                  child: Transform.scale(
                    scale: value,
                    child: Image.asset(
                      'assets/images/lc-logo.png',
                      width: 120,
                      height: 120,
                    ),
                  ),
                );
              },
            ),
            Text(
              'អិលស៊ី ឃេស អិចប្រេស​ ​ឯ.ក',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: const Color.fromARGB(255, 18, 43, 114),
              ),
              semanticsLabel: 'អិលស៊ី ឃេស អិចប្រេស​ ឯ.ក',
            ),
            const Text(
              'កាន់តែរហ័ស កាន់តែប្រសើរ និង កាន់តែច្រើន',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color.fromARGB(255, 152, 245, 3),
              ),
            ),
            const SizedBox(height: 20),
            const CircularProgressIndicator(
              color: Color.fromARGB(255, 18, 43, 114),
            ),
          ],
        ),
      ),
    );
  }
}
