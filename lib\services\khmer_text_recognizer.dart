import 'dart:io';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';

class KhmerTextRecognizer {
  /// Recognizes text from the given image file and returns the lines as a list of strings.
  static Future<List<String>> recognizeTextLines(File imageFile) async {
    final inputImage = InputImage.fromFile(imageFile);
    final textRecognizer = TextRecognizer(script: TextRecognitionScript.latin);
    final RecognizedText recognizedText = await textRecognizer.processImage(
      inputImage,
    );
    await textRecognizer.close();
    // Return all lines (for MRZ, usually last 2-3 lines)
    return recognizedText.blocks
        .expand((b) => b.lines.map((l) => l.text))
        .toList();
  }

  /// Extracts MRZ lines from the recognized text lines (looks for lines with many '<' characters)
  static List<String> extractMRZLines(List<String> lines) {
    // MRZ lines typically have many '<' characters and are of similar length
    final mrzLines =
        lines
            .where(
              (line) =>
                  line.replaceAll(' ', '').contains('<') && line.length >= 25,
            )
            .toList();
    // Return the last 2 or 3 lines (MRZ is usually at the bottom)
    if (mrzLines.length >= 2) {
      return mrzLines.sublist(mrzLines.length - (mrzLines.length >= 3 ? 3 : 2));
    }
    return [];
  }

  /// Extracts the full name (Latin) from OCR lines.
  static String? extractFullNameLatin(List<String> lines) {
    final match =
        lines
            .firstWhere(
              (line) =>
                  RegExp(r'^[A-Z ]+$').hasMatch(line.trim()) &&
                  line.trim().contains(' '),
              orElse: () => '',
            )
            .trim();
    return match.isEmpty ? null : match;
  }

  /// Extracts the ID number from OCR lines (looks for 'ID:').
  static String? extractIdNumber(List<String> lines) {
    final idLine = lines.firstWhere(
      (line) => line.trim().startsWith('ID:'),
      orElse: () => '',
    );
    return idLine.isNotEmpty ? idLine.replaceAll('ID:', '').trim() : null;
  }

  /// Extracts the date of birth from OCR lines (matches DD-MM-YYYY).
  static String? extractDateOfBirth(List<String> lines) {
    final match = lines.firstWhere(
      (line) => RegExp(r'\d{2}-\d{2}-\d{4}').hasMatch(line),
      orElse: () => '',
    );
    return match.isEmpty ? null : match;
  }
}
