// MRZ Parser for ID Card/Passport (TD1/TD2/TD3)
// Returns a map with keys: 'fullNameLatin', 'idNumber', 'dateOfBirth'

class MRZResult {
  final String fullNameLatin;
  final String idNumber;
  final String dateOfBirth; // Format: YYYY-MM-DD

  MRZResult({
    required this.fullNameLatin,
    required this.idNumber,
    required this.dateOfBirth,
  });
}

class MRZParser {
  /// Parses MRZ lines and extracts full name (Latin), ID number, and date of birth.
  /// [mrzLines] should be a list of MRZ lines (usually 2 or 3 lines).
  static MRZResult? parse(List<String> mrzLines) {
    if (mrzLines.isEmpty) return null;
    // Support for TD1 (3 lines), TD2 (2 lines), TD3 (2 lines)
    final lines =
        mrzLines
            .map(
              (l) => l
                  .replaceAll(' ', '')
                  .replaceAll('\n', '')
                  .replaceAll('\r', ''),
            )
            .toList();
    if (lines.length == 3) {
      // TD1 (ID card, 3 lines, 30 chars each)
      final line1 = lines[0].padRight(30, '<');
      final line2 = lines[1].padRight(30, '<');
      final line3 = lines[2].padRight(30, '<');
      final idNumber = line1.substring(5, 14).replaceAll('<', '');
      final dobRaw = line2.substring(0, 6); // YYMMDD
      final dateOfBirth = _parseDateYYMMDD(dobRaw);
      final namesRaw = line3.substring(0, 30).split('<<');
      final surname = namesRaw[0].replaceAll('<', ' ').trim();
      final givenNames =
          namesRaw.length > 1 ? namesRaw[1].replaceAll('<', ' ').trim() : '';
      final fullNameLatin = ('$surname $givenNames').trim();
      return MRZResult(
        fullNameLatin: fullNameLatin,
        idNumber: idNumber,
        dateOfBirth: dateOfBirth,
      );
    } else if (lines.length == 2 && lines[0].length >= 44) {
      // TD3 (passport, 2 lines, 44 chars each)
      final line1 = lines[0].padRight(44, '<');
      final line2 = lines[1].padRight(44, '<');
      final idNumber = line2.substring(0, 9).replaceAll('<', '');
      final dobRaw = line2.substring(13, 19); // YYMMDD
      final dateOfBirth = _parseDateYYMMDD(dobRaw);
      final namesRaw = line1.substring(5, 44).split('<<');
      final surname = namesRaw[0].replaceAll('<', ' ').trim();
      final givenNames =
          namesRaw.length > 1 ? namesRaw[1].replaceAll('<', ' ').trim() : '';
      final fullNameLatin = ('$surname $givenNames').trim();
      return MRZResult(
        fullNameLatin: fullNameLatin,
        idNumber: idNumber,
        dateOfBirth: dateOfBirth,
      );
    }
    // Not a recognized MRZ format
    return null;
  }

  static String _parseDateYYMMDD(String yymmdd) {
    if (yymmdd.length != 6) return '';
    final year = int.parse(yymmdd.substring(0, 2));
    final month = yymmdd.substring(2, 4);
    final day = yymmdd.substring(4, 6);
    // Heuristic: if year > current year, it's 1900s, else 2000s
    final nowYear = DateTime.now().year % 100;
    final century = (year > nowYear) ? 1900 : 2000;
    return '${century + year}-$month-$day';
  }
}
