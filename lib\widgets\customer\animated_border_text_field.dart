import 'package:flutter/material.dart';

/// A text field with animated border color/width on focus, for modern UX.
class AnimatedBorderTextField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String? hint;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final int maxLines;
  final bool enabled;

  const AnimatedBorderTextField({
    super.key,
    required this.controller,
    required this.label,
    this.hint,
    this.validator,
    this.keyboardType,
    this.maxLines = 1,
    this.enabled = true,
  });

  @override
  State<AnimatedBorderTextField> createState() => _AnimatedBorderTextFieldState();
}

class _AnimatedBorderTextFieldState extends State<AnimatedBorderTextField> {
  late FocusNode _focusNode;
  bool _hasFocus = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(_handleFocusChange);
  }

  void _handleFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
    });
  }

  @override
  void dispose() {
    _focusNode.removeListener(_handleFocusChange);
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 250),
      curve: Curves.ease,
      decoration: BoxDecoration(
        border: Border.all(
          color: _hasFocus ? const Color(0xFF12306E) : Colors.grey[300]!,
          width: _hasFocus ? 2.2 : 1.2,
        ),
        borderRadius: BorderRadius.circular(8),
        color: widget.enabled ? Colors.white : Colors.grey[100],
      ),
      child: TextFormField(
        controller: widget.controller,
        focusNode: _focusNode,
        enabled: widget.enabled,
        maxLines: widget.maxLines,
        keyboardType: widget.keyboardType,
        validator: widget.validator,
        style: const TextStyle(
          fontFamily: 'KontomroyPro',
          fontSize: 12,
          color: Colors.black87,
        ),
        decoration: InputDecoration(
          labelText: widget.label,
          labelStyle: const TextStyle(
            fontFamily: 'KontomroyPro',
            fontSize: 12,
            color: Colors.black87,
          ),
          hintText: widget.hint,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        ),
      ),
    );
  }
}
