import 'package:flutter/material.dart';
/// Animated checkmark pop for step completion
class Animated<PERSON><PERSON><PERSON><PERSON> extends StatefulWidget {
  final bool visible;
  final Duration duration;

  const AnimatedCheckmark({
    super.key,
    required this.visible,
    this.duration = const Duration(milliseconds: 450),
  });

  @override
  State<AnimatedCheckmark> createState() => _AnimatedCheckmarkState();
}

class _AnimatedCheckmarkState extends State<AnimatedCheckmark>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnim;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _scaleAnim = CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    );
    if (widget.visible) _controller.forward();
  }

  @override
  void didUpdateWidget(covariant AnimatedCheckmark oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.visible && !oldWidget.visible) {
      _controller.forward(from: 0);
    } else if (!widget.visible && oldWidget.visible) {
      _controller.reverse();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnim,
      child: Icon(
        Icons.check_circle,
        color: Colors.greenAccent.shade700,
        size: 28,
      ),
    );
  }
}
