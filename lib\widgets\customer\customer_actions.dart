import 'package:flutter/material.dart';

class CustomerActions extends StatelessWidget {
  final VoidCallback onRecordPayment;
  final VoidCallback onEditLoan;
  final VoidCallback onViewPaymentHistory;

  const CustomerActions({
    super.key,
    required this.onRecordPayment,
    required this.onEditLoan,
    required this.onViewPaymentHistory,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: onRecordPayment,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  side: const BorderSide(color: Color(0xFF12306E)),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                icon: const Icon(Icons.payment, color: Color(0xFF12306E)),
                label: const Text(
                  'Record Payment',
                  style: TextStyle(color: Color(0xFF12306E)),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: onEditLoan,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF12306E),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                icon: const Icon(Icons.edit_document, color: Colors.white),
                label: const Text(
                  'Edit Loan',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: onViewPaymentHistory,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              side: BorderSide(color: Colors.grey[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: const Icon(Icons.history, color: Colors.grey),
            label: const Text(
              'View Payment History',
              style: TextStyle(color: Colors.grey),
            ),
          ),
        ),
      ],
    );
  }
}
