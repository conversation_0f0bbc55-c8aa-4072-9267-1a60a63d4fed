import 'package:flutter/material.dart';

class CustomerDatePickerField extends StatelessWidget {
  final String label;
  final DateTime? selectedDate;
  final Function(DateTime) onDateSelected;
  final String? Function(DateTime?)? validator;
  final bool enabled;

  const CustomerDatePickerField({
    super.key,
    required this.label,
    required this.selectedDate,
    required this.onDateSelected,
    this.validator,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: GestureDetector(
      onTap: enabled ? () async {
        final picked = await showDatePicker(
          context: context,
          initialDate: selectedDate ?? DateTime.now(),
          firstDate: DateTime(1900),
          lastDate: DateTime(2100),
        );
        if (picked != null) {
          onDateSelected(picked);
        }
      } : null,
      child: AbsorbPointer(
        child: TextFormField(
          enabled: enabled,
          decoration: InputDecoration(
            labelText: label,
            labelStyle: const TextStyle(
              fontFamily: 'Konto<PERSON>royPro',
              fontSize: 14,
              color: Colors.black87,
            ),
            border: const OutlineInputBorder(),
          ),
          controller: TextEditingController(
            text:
                selectedDate != null
                    ? "${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}"
                    : '',
          ),
          style: const TextStyle(
            fontFamily: 'KontomroyPro',
            fontSize: 14,
            color: Colors.black87,
          ),
          validator: (value) {
            if (validator != null) {
              return validator!(selectedDate);
            }
            return null;
          },
        ),
      ),
      ),
    );
  }
}
