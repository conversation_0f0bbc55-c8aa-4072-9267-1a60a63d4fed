import 'package:flutter/material.dart';
import 'dart:io';

import '../../../models/customer_model.dart';

class CustomerDetailsHeader extends StatelessWidget {
  final Customer customer;

  const CustomerDetailsHeader({
    super.key,
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CircleAvatar(
              radius: 32,
              backgroundColor: const Color(0xFFF0F4FF),
              backgroundImage: customer.profileImage != null
                  ? NetworkImage(customer.profileImage!)
                  : (customer.profilePhotoPath != null
                      ? FileImage(File(customer.profilePhotoPath!))
                      : null),
              child: (customer.profileImage == null && customer.profilePhotoPath == null)
                  ? Text(
                      customer.name.isNotEmpty ? customer.name[0] : '',
                      style: textTheme.headlineMedium?.copyWith(
                        color: const Color(0xFF12306E),
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    customer.name,
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      const Icon(
                        Icons.phone,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        customer.phone,
                        style: textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  _buildStatusChip(customer),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(Customer customer) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: customer.statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: customer.statusColor),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            customer.statusIcon,
            size: 14,
            color: customer.statusColor,
          ),
          const SizedBox(width: 4),
          Text(
            customer.loanStatus.toString().split('.').last.toUpperCase(),
            style: TextStyle(
              color: customer.statusColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
