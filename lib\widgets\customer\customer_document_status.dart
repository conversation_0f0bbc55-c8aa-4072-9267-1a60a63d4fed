import 'package:flutter/material.dart';

class CustomerDocumentStatus extends StatelessWidget {
  final String label;
  final bool isUploaded;
  const CustomerDocumentStatus({super.key, required this.label, required this.isUploaded});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            isUploaded ? Icons.check_circle : Icons.radio_button_unchecked,
            color: isUploaded ? Colors.green : Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(
              fontFamily: 'KontomroyPro',
              fontSize: 14,
              color: isUploaded ? Colors.black87 : Colors.grey[600],
              fontWeight: isUploaded ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}
