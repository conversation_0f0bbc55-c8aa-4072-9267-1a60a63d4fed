import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lc_work_flow/models/customer_model.dart';
import 'package:lc_work_flow/widgets/customer/image_preview_screen.dart';

class CustomerDocumentsSection extends StatelessWidget {
  final Customer customer;

  const CustomerDocumentsSection({super.key, required this.customer});

  @override
  Widget build(BuildContext context) {
    final documents = _collectDocuments();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Documents',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(height: 16),
        if (documents.isEmpty)
          const Center(
            child: Text('No documents available.'),
          )
        else
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: documents.length,
            separatorBuilder: (context, index) => const SizedBox(height: 16),
            itemBuilder: (context, index) {
              final doc = documents[index];
              return _buildDocumentCard(context, doc.value, doc.key);
            },
          ),
      ],
    );
  }

  List<MapEntry<String, String>> _collectDocuments() {
    final docs = <String, String>{};

    if (customer.idCardPhotoPath != null) {
      docs['ID Card'] = customer.idCardPhotoPath!;
    }
    if (customer.borrowerNidPhotoPath != null) {
      docs['Borrower NID'] = customer.borrowerNidPhotoPath!;
    }
    if (customer.borrowerHomeOrLandPhotoPath != null) {
      docs['Borrower Home/Land'] = customer.borrowerHomeOrLandPhotoPath!;
    }
    if (customer.borrowerBusinessPhotoPath != null) {
      docs['Borrower Business'] = customer.borrowerBusinessPhotoPath!;
    }
    if (customer.guarantorNidPhotoPath != null) {
      docs['Guarantor NID'] = customer.guarantorNidPhotoPath!;
    }
    if (customer.guarantorHomeOrLandPhotoPath != null) {
      docs['Guarantor Home/Land'] = customer.guarantorHomeOrLandPhotoPath!;
    }
    if (customer.guarantorBusinessPhotoPath != null) {
      docs['Guarantor Business'] = customer.guarantorBusinessPhotoPath!;
    }

    return docs.entries.toList();
  }

  Widget _buildDocumentCard(BuildContext context, String imagePath, String label) {
    return GestureDetector(
      onTap: () {
        Get.to(() => ImagePreviewScreen(imagePath: imagePath, label: label));
      },
      child: Card(
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 2,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            AspectRatio(
              aspectRatio: 16 / 9,
              child: Image.file(
                File(imagePath),
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return const Center(
                    child: Icon(Icons.broken_image, size: 40, color: Colors.grey),
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Text(
                label,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.titleSmall,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
