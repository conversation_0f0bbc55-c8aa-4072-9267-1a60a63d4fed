import 'package:flutter/material.dart';
import 'animated_checkmark.dart';

class CustomerStepIndicator extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final List<String> stepTitles;
  final void Function(int)? onStepTapped;

  const CustomerStepIndicator({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepTitles,
    this.onStepTapped,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Progress bar
        Stack(
          children: [
            Container(
              height: 6,
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(3),
              ),
            ),
            AnimatedContainer(
              duration: const Duration(milliseconds: 400),
              height: 6,
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              width: MediaQuery.of(context).size.width * ((currentStep + 1) / totalSteps),
              decoration: BoxDecoration(
                color: const Color(0xFF12306E),
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(totalSteps, (index) {
            final isActive = index == currentStep;
            final isCompleted = index < currentStep;
            return Expanded(
              child: GestureDetector(
                onTap: onStepTapped != null ? () => onStepTapped!(index) : null,
                child: Column(
                  children: [
                    CircleAvatar(
                      radius: isActive ? 16 : 14,
                      backgroundColor: isCompleted || isActive ? const Color(0xFF12306E) : Colors.grey[300],
                      child: isCompleted
                          ? AnimatedCheckmark(visible: isCompleted)
                          : Center(
                              child: Icon(
                                [Icons.person_outline, Icons.account_balance, Icons.file_copy, Icons.check_circle][index],
                                color: isCompleted || isActive ? Colors.white : Colors.grey[600],
                                size: 20,
                              ),
                            ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      stepTitles[index],
                      style: TextStyle(
                        fontFamily: 'KontomroyPro',
                        fontSize: 12,
                        color: isCompleted || isActive ? const Color(0xFF12306E) : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ),
      ],
    );
  }
}
