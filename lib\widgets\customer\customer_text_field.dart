import 'package:flutter/material.dart';
import 'animated_border_text_field.dart';

class CustomerTextField extends StatelessWidget {
  final String label;
  final TextEditingController controller;
  final String? hint;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final bool isRequired;
  final int? maxLines;
  final bool enabled;

  const CustomerTextField({
    super.key,
    required this.label,
    required this.controller,
    this.hint,
    this.validator,
    this.keyboardType,
    this.isRequired = true,
    this.maxLines = 1,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: AnimatedBorderTextField(
        controller: controller,
        label: label,
        hint: hint,
        validator: validator,
        keyboardType: keyboardType,
        maxLines: maxLines ?? 1,
        enabled: enabled,
      ),
    );
  }
}
