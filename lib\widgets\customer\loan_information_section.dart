import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../models/customer_model.dart';

class LoanInformationSection extends StatelessWidget {
  final Customer customer;

  const LoanInformationSection({
    super.key,
    required this.customer,
  });

  @override
  Widget build(BuildContext context) {
    final currencyFormat = NumberFormat.currency(
      locale: 'km',
      symbol: '៛',
      decimalDigits: 0,
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Loan Information',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: const Color(0xFF12306E),
              ),
        ),
        const SizedBox(height: 12),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                _buildDetailRow(
                  context,
                  'Loan Amount',
                  currencyFormat.format(customer.loanAmount),
                  Icons.money,
                  Colors.green,
                ),
                const Divider(height: 24),
                _buildDetailRow(
                  context,
                  'Interest Rate',
                  '${customer.interestRate ?? 0.0}%',
                  Icons.percent,
                  Colors.orange,
                ),
                const Divider(height: 24),
                _buildDetailRow(
                  context,
                  'Start Date',
                  customer.formattedStartDate,
                  Icons.calendar_today,
                  Colors.blue,
                ),
                if (customer.loanEndDate != null) ...[
                  const Divider(height: 24),
                  _buildDetailRow(
                    context,
                    'End Date',
                    DateFormat('MMM d, y').format(customer.loanEndDate!),
                    Icons.calendar_today_outlined,
                    Colors.purple,
                  ),
                ],
                if (customer.loanPurpose != null) ...[
                  const Divider(height: 24),
                  _buildDetailRow(
                    context,
                    'Purpose',
                    customer.loanPurpose!,
                    Icons.description,
                    Colors.teal,
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color iconColor,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 20, color: iconColor),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
